<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SocialMuse - Standalone Version</title>
    <style>
        :root {
            --primary: #6d28d9;
            --primary-light: #8b5cf6;
            --primary-dark: #5b21b6;
            --background: #f8fafc;
            --text: #1e293b;
            --card-bg: #ffffff;
            --border: #e2e8f0;
            
            --linkedin: #0a66c2;
            --instagram: #e4405f;
            --twitter: #1DA1F2;
            --facebook: #1877f2;
            --youtube: #ff0000;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--background);
            color: var(--text);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--border);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            margin: 0;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-size: 24px;
            font-weight: bold;
        }
        
        .controls {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .button:hover {
            background-color: var(--primary-dark);
        }
        
        .button-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .button-outline:hover {
            background-color: rgba(109, 40, 217, 0.1);
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 24px;
            height: 24px;
        }
        
        .card-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .linkedin-card {
            border-top: 4px solid var(--linkedin);
        }
        
        .instagram-card {
            border-top: 4px solid var(--instagram);
        }
        
        .twitter-card {
            border-top: 4px solid var(--twitter);
        }
        
        .facebook-card {
            border-top: 4px solid var(--facebook);
        }
        
        .youtube-card {
            border-top: 4px solid var(--youtube);
        }
        
        .card-title.linkedin {
            color: var(--linkedin);
        }
        
        .card-title.instagram {
            color: var(--instagram);
        }
        
        .card-title.twitter {
            color: var(--twitter);
        }
        
        .card-title.facebook {
            color: var(--facebook);
        }
        
        .card-title.youtube {
            color: var(--youtube);
        }
        
        textarea {
            width: 100%;
            min-height: 120px;
            border: 1px solid var(--border);
            border-radius: 4px;
            padding: 10px;
            font-family: inherit;
            resize: vertical;
            margin-bottom: 10px;
        }
        
        .card-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }
        
        .youtube-section {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-top: 4px solid var(--youtube);
        }
        
        .youtube-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .youtube-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--youtube);
        }
        
        .youtube-form {
            display: grid;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        label {
            font-weight: 500;
            font-size: 14px;
        }
        
        input {
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-family: inherit;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
            position: relative;
        }
        
        .loading::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px 0;
            color: #64748b;
            font-size: 14px;
        }
        
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary);
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.error {
            background-color: #ef4444;
        }
        
        .toast.success {
            background-color: #10b981;
        }
        
        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>SocialMuse</h1>
        </div>
    </header>
    
    <div class="container">
        <div class="controls">
            <button id="generateCommon" class="button">Generate Common Content</button>
            <button id="pasteToAll" class="button button-outline">Paste to All Platforms</button>
            <button id="postToAll" class="button">Post to All Platforms</button>
        </div>
        
        <div class="card-grid">
            <div class="card linkedin-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="#0a66c2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M6 9H2V21H6V9Z" stroke="#0a66c2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="#0a66c2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <h3 class="card-title linkedin">LinkedIn</h3>
                </div>
                <textarea id="linkedinContent" placeholder="Enter your LinkedIn content here..."></textarea>
                <div class="card-actions">
                    <button class="button" onclick="generateContent('linkedin')">Generate</button>
                    <button class="button button-outline" onclick="pasteCommon('linkedin')">Paste Common</button>
                </div>
            </div>
            
            <div class="card instagram-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 2H7C4.23858 2 2 4.23858 2 7V17C2 19.7614 4.23858 22 7 22H17C19.7614 22 22 19.7614 22 17V7C22 4.23858 19.7614 2 17 2Z" stroke="#e4405f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 11.37C16.1234 12.2022 15.9813 13.0522 15.5938 13.799C15.2063 14.5458 14.5931 15.1514 13.8416 15.5297C13.0901 15.9079 12.2384 16.0396 11.4078 15.9059C10.5771 15.7723 9.80976 15.3801 9.21484 14.7852C8.61992 14.1902 8.22773 13.4229 8.09407 12.5922C7.9604 11.7615 8.09207 10.9099 8.47033 10.1584C8.84859 9.40685 9.45419 8.79374 10.201 8.40624C10.9478 8.01874 11.7978 7.87659 12.63 8C13.4789 8.12588 14.2649 8.52146 14.8717 9.1283C15.4785 9.73515 15.8741 10.5211 16 11.37Z" stroke="#e4405f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M17.5 6.5H17.51" stroke="#e4405f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <h3 class="card-title instagram">Instagram</h3>
                </div>
                <textarea id="instagramContent" placeholder="Enter your Instagram content here..."></textarea>
                <div class="card-actions">
                    <button class="button" onclick="generateContent('instagram')">Generate</button>
                    <button class="button button-outline" onclick="pasteCommon('instagram')">Paste Common</button>
                </div>
            </div>
            
            <div class="card twitter-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M23 3C22.0424 3.67548 20.9821 4.19211 19.86 4.53C19.2577 3.83751 18.4573 3.34669 17.567 3.12393C16.6767 2.90116 15.7395 2.9572 14.8821 3.28445C14.0247 3.61171 13.2884 4.1944 12.773 4.95372C12.2575 5.71303 11.9877 6.61234 12 7.53V8.53C10.2426 8.57557 8.50127 8.18581 6.93101 7.39545C5.36074 6.60508 4.01032 5.43864 3 4C3 4 -1 13 8 17C5.94053 18.398 3.48716 19.0989 1 19C10 24 21 19 21 7.5C20.9991 7.22145 20.9723 6.94359 20.92 6.67C21.9406 5.66349 22.6608 4.39271 23 3Z" stroke="#1DA1F2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <h3 class="card-title twitter">Twitter</h3>
                </div>
                <textarea id="twitterContent" placeholder="Enter your Twitter content here..."></textarea>
                <div class="card-actions">
                    <button class="button" onclick="generateContent('twitter')">Generate</button>
                    <button class="button button-outline" onclick="pasteCommon('twitter')">Paste Common</button>
                </div>
            </div>
            
            <div class="card facebook-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z" stroke="#1877f2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <h3 class="card-title facebook">Facebook</h3>
                </div>
                <textarea id="facebookContent" placeholder="Enter your Facebook content here..."></textarea>
                <div class="card-actions">
                    <button class="button" onclick="generateContent('facebook')">Generate</button>
                    <button class="button button-outline" onclick="pasteCommon('facebook')">Paste Common</button>
                </div>
            </div>
        </div>
        
        <div class="youtube-section">
            <div class="youtube-header">
                <svg class="card-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.54 6.42C22.4212 5.94541 22.1793 5.51057 21.8387 5.15941C21.498 4.80824 21.0708 4.55318 20.6 4.42C18.88 4 12 4 12 4C12 4 5.12 4 3.4 4.46C2.92922 4.59318 2.50197 4.84824 2.16134 5.19941C1.82071 5.55057 1.57878 5.98541 1.46 6.46C1.14521 8.20556 0.991235 9.97631 1 11.75C0.988687 13.537 1.14266 15.3213 1.46 17.08C1.57959 17.5398 1.82069 17.9581 2.15846 18.3016C2.49623 18.6451 2.91872 18.897 3.38 19.02C5.1 19.46 12 19.46 12 19.46C12 19.46 18.88 19.46 20.6 19.02C21.0708 18.8868 21.498 18.6318 21.8387 18.2806C22.1793 17.9294 22.4212 17.4946 22.54 17.02C22.8524 15.2756 23.0063 13.5083 23 11.73C23.0113 9.94298 22.8573 8.15866 22.54 6.4V6.42Z" stroke="#ff0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M9.75 15.02L15.5 11.75L9.75 8.48001V15.02Z" stroke="#ff0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <h3 class="youtube-title">YouTube</h3>
            </div>
            
            <div class="youtube-form">
                <div class="form-group">
                    <label for="youtubeTitle">Title</label>
                    <input type="text" id="youtubeTitle" placeholder="Enter video title...">
                </div>
                
                <div class="form-group">
                    <label for="youtubeDescription">Description</label>
                    <textarea id="youtubeDescription" placeholder="Enter video description..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="youtubeTags">Tags (comma separated)</label>
                    <input type="text" id="youtubeTags" placeholder="productivity,business,marketing...">
                </div>
                
                <div class="card-actions">
                    <button class="button" onclick="generateContent('youtube')">Generate Content</button>
                    <button class="button" onclick="postToYouTube()">Post to YouTube</button>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <p>SocialMuse © 2025 - The ultimate social media content generator</p>
    </footer>
    
    <div id="toast" class="toast"></div>
    
    <script>
        // State variables
        let commonContent = '';
        let isGenerating = false;
        
        // DOM Elements
        const generateCommonBtn = document.getElementById('generateCommon');
        const pasteToAllBtn = document.getElementById('pasteToAll');
        const postToAllBtn = document.getElementById('postToAll');
        
        const linkedinTextarea = document.getElementById('linkedinContent');
        const instagramTextarea = document.getElementById('instagramContent');
        const twitterTextarea = document.getElementById('twitterContent');
        const facebookTextarea = document.getElementById('facebookContent');
        
        const youtubeTitle = document.getElementById('youtubeTitle');
        const youtubeDescription = document.getElementById('youtubeDescription');
        const youtubeTags = document.getElementById('youtubeTags');
        
        const toast = document.getElementById('toast');
        
        // Event Listeners
        generateCommonBtn.addEventListener('click', () => generateContent('common'));
        pasteToAllBtn.addEventListener('click', pasteToAll);
        postToAllBtn.addEventListener('click', postToAll);
        
        // Functions
        function showToast(message, type = 'info') {
            toast.textContent = message;
            toast.className = 'toast show ' + type;
            
            setTimeout(() => {
                toast.className = 'toast';
            }, 3000);
        }
        
        function setLoading(isLoading) {
            isGenerating = isLoading;
            const buttons = document.querySelectorAll('.button');
            
            buttons.forEach(button => {
                if (isLoading) {
                    button.classList.add('loading');
                    button.disabled = true;
                } else {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
        }
        
        function generateContent(platform) {
            setLoading(true);
            
            // Simulate API delay
            setTimeout(() => {
                const platformSpecificContent = {
                    linkedin: "Excited to share our latest innovation that's transforming how teams collaborate. Our new platform increases productivity by 35% while reducing meeting time. #Innovation #Productivity #WorkSmarter",
                    instagram: "✨ New day, new possibilities! Check out what we've been working on behind the scenes. This game-changing solution is about to make your workflow so much smoother! Double tap if you're ready for the future of work. 🚀 #WorkLifeBalance #Innovation",
                    twitter: "Just launched our game-changing productivity tool! 35% boost in team efficiency with 50% fewer meetings. Try the free demo today: [link] #ProductivityHack",
                    facebook: "We're thrilled to announce the launch of our new productivity platform! After months of development and testing with our amazing beta users, we're ready to share it with the world. This tool has been shown to increase team productivity by 35% while reducing meeting time by half. Click the link to learn more and start your free trial!",
                    youtube: {
                        title: "Revolutionary Productivity Tool | How We Increased Efficiency by 35%",
                        description: "In this video, we walk through our new productivity platform that's changing how teams work together. We'll show you the key features that help reduce meeting time and boost overall efficiency, plus share some success stories from our beta testers.\n\nTimestamps:\n0:00 Introduction\n1:23 The Problem with Modern Workflows\n3:45 Our Solution\n5:30 Key Features\n8:15 Case Study: How Team X Saved 10 Hours Per Week\n10:30 Pricing and How to Get Started",
                        tags: "productivity,efficiency,remote work,collaboration,meetings,time management,work tools,productivity app"
                    },
                    common: "Introducing our revolutionary new platform that transforms team collaboration and productivity. Early users report 35% efficiency gains and significantly reduced meeting time. Learn more at our website."
                };
                
                if (platform === 'linkedin') {
                    linkedinTextarea.value = platformSpecificContent.linkedin;
                    showToast('LinkedIn content generated!', 'success');
                } else if (platform === 'instagram') {
                    instagramTextarea.value = platformSpecificContent.instagram;
                    showToast('Instagram content generated!', 'success');
                } else if (platform === 'twitter') {
                    twitterTextarea.value = platformSpecificContent.twitter;
                    showToast('Twitter content generated!', 'success');
                } else if (platform === 'facebook') {
                    facebookTextarea.value = platformSpecificContent.facebook;
                    showToast('Facebook content generated!', 'success');
                } else if (platform === 'youtube') {
                    youtubeTitle.value = platformSpecificContent.youtube.title;
                    youtubeDescription.value = platformSpecificContent.youtube.description;
                    youtubeTags.value = platformSpecificContent.youtube.tags;
                    showToast('YouTube content generated!', 'success');
                } else if (platform === 'common') {
                    commonContent = platformSpecificContent.common;
                    showToast('Common content generated! Use "Paste to All" to apply it.', 'success');
                }
                
                setLoading(false);
            }, 1000);
        }
        
        function pasteCommon(platform) {
            if (!commonContent) {
                showToast('Generate common content first!', 'error');
                return;
            }
            
            if (platform === 'linkedin') {
                linkedinTextarea.value = commonContent;
            } else if (platform === 'instagram') {
                instagramTextarea.value = commonContent;
            } else if (platform === 'twitter') {
                twitterTextarea.value = commonContent;
            } else if (platform === 'facebook') {
                facebookTextarea.value = commonContent;
            }
            
            showToast(`Common content pasted to ${platform}!`, 'success');
        }
        
        function pasteToAll() {
            if (!commonContent) {
                showToast('Generate common content first!', 'error');
                return;
            }
            
            linkedinTextarea.value = commonContent;
            instagramTextarea.value = commonContent;
            twitterTextarea.value = commonContent;
            facebookTextarea.value = commonContent;
            
            showToast('Common content pasted to all platforms!', 'success');
        }
        
        function postToAll() {
            setLoading(true);
            
            // Simulate API delay
            setTimeout(() => {
                showToast('Content posted to all platforms!', 'success');
                setLoading(false);
            }, 1500);
        }
        
        function postToYouTube() {
            if (!youtubeTitle.value || !youtubeDescription.value) {
                showToast('Please fill in the title and description!', 'error');
                return;
            }
            
            setLoading(true);
            
            // Simulate API delay
            setTimeout(() => {
                showToast('Content posted to YouTube!', 'success');
                setLoading(false);
            }, 1500);
        }
    </script>
</body>
</html>
