<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SocialMuse</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            margin-top: 20px;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .platform-card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .platform-card h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <header>
        <h1>SocialMuse</h1>
        <p>Generate optimized content for all your social media platforms</p>
    </header>
    
    <div class="container">
        <div class="content">
            <h2>Welcome to SocialMuse</h2>
            <p>Use our AI-powered tool to create engaging content for multiple social media platforms with just a few clicks.</p>
            
            <div class="platform-grid">
                <div class="platform-card">
                    <h3>LinkedIn</h3>
                    <p>Professional content optimized for business networking.</p>
                    <button class="button">Generate Content</button>
                </div>
                
                <div class="platform-card">
                    <h3>Instagram</h3>
                    <p>Visual-focused content with engaging captions.</p>
                    <button class="button">Generate Content</button>
                </div>
                
                <div class="platform-card">
                    <h3>Twitter</h3>
                    <p>Short, impactful messages with relevant hashtags.</p>
                    <button class="button">Generate Content</button>
                </div>
                
                <div class="platform-card">
                    <h3>Facebook</h3>
                    <p>Engaging posts designed for community interaction.</p>
                    <button class="button">Generate Content</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simple script to show the app is working
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    alert('Content generation would happen here in the full app!');
                });
            });
        });
    </script>
</body>
</html>
